<!-- installerLaunch4j-x64.xml

     Sweet Home 3D, Copyright (c) 2024 Space Mushrooms <<EMAIL>>

     SweetHome3D.exe creator 
     This script requires launch4j available at http://launch4j.sourceforge.net/     
     and a SweetHome3D.ico file containing Sweet Home 3D icon.
     It creates a SweetHome3D.exe in build directory running with
     JRE or OpenJDK stored in runtime subdirectory 
     + SweetHome3D.jar, Windows Java 3D DLLs and JARs for Java 3D and other required JARs 
       stored in a lib subdirectory -->
<launch4jConfig>
  <dontWrapJar>true</dontWrapJar>
  <headerType>gui</headerType>
  <jar></jar>
  <outfile>build\SweetHome3D-x64.exe</outfile>
  <errTitle></errTitle>
  <cmdLine></cmdLine>
  <chdir>.</chdir>
  <priority>normal</priority>
  <downloadUrl>http://java.com/download</downloadUrl>
  <supportUrl></supportUrl>
  <customProcName>true</customProcName>
  <stayAlive>false</stayAlive>
  <icon>SweetHome3D.ico</icon>
  <classPath>
    <mainClass>com.eteks.sweethome3d.SweetHome3D</mainClass>
    <cp>lib/SweetHome3D.jar</cp>
    <cp>lib/Furniture.jar</cp>
    <cp>lib/Textures.jar</cp>
    <cp>lib/Examples.jar</cp>
    <cp>lib/Help.jar</cp>
    <cp>lib/batik-svgpathparser-1.7.jar</cp>
    <cp>lib/jeksparser-calculator.jar</cp>
    <cp>lib/iText-2.1.7.jar</cp>
    <cp>lib/freehep-vectorgraphics-svg-2.1.1c.jar</cp>
    <cp>lib/sunflow-0.07.3i.jar</cp>
    <cp>lib/jmf.jar</cp>
    <cp>lib/java3d-1.6/j3dcore.jar</cp>
    <cp>lib/java3d-1.6/j3dutils.jar</cp>
    <cp>lib/java3d-1.6/vecmath.jar</cp>
    <cp>lib/java3d-1.6/gluegen-rt.jar</cp>
    <cp>lib/java3d-1.6/jogl-java3d.jar</cp>
    <cp>lib/jnlp.jar</cp>
  </classPath>
  <jre>
    <path>runtime</path>
    <bundledJre64Bit>true</bundledJre64Bit>
    <minVersion>1.5.0</minVersion>
    <maxVersion></maxVersion>
    <dontUsePrivateJres>false</dontUsePrivateJres>
    <initialHeapSize>0</initialHeapSize>
    <maxHeapSize>2048</maxHeapSize>
    <maxHeapPercent>50</maxHeapPercent>
    <opt>-Djava.library.path="lib/java3d-1.6;lib/yafaray" -Djogamp.gluegen.UseTempJarCache=false -Djava.locale.providers="COMPAT,SPI" -Dcom.eteks.sweethome3d.applicationId=SweetHome3D#Installer -Dcom.eteks.sweethome3d.checkUpdates=%HKEY_CLASSES_ROOT\Sweet Home 3D\Settings\checkUpdates%</opt>
  </jre>
  <versionInfo>
    <fileVersion>*******</fileVersion>
    <txtFileVersion>7.5</txtFileVersion>
    <fileDescription>Sweet Home 3D</fileDescription>
    <copyright>Copyright (c) 2024 Space Mushrooms</copyright>
    <productVersion>*******</productVersion>
    <txtProductVersion>7.5</txtProductVersion>
    <productName>Sweet Home 3D</productName>
    <companyName>Space Mushrooms</companyName>
    <internalName>SweetHome3D</internalName>
    <originalFilename>SweetHome3D.exe</originalFilename>
  </versionInfo>
</launch4jConfig>