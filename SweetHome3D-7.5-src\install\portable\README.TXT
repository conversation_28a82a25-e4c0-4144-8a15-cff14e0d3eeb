SWEET HOME 3D v 7.5
-------------------

The current folder contains the required applications and data to run Sweet Home 3D
under Windows 32 bits, Windows 64 bits, Linux 32 bits and Linux 64 bits.

To use Sweet Home 3D, simply copy the current folder on a fixed or a removable drive, 
and run the application matching your operating system:
- SweetHome3D-windows-x86 for Windows 32 bits
- SweetHome3D-windows-x64 for Windows 64 bits
- SweetHome3D-linux-x86   for Linux 32 bits
- SweetHome3D-linux-x64   for Linux 64 bits

This version comes with the 1610 pieces of furniture and 423 textures available at:
http://www.sweethome3d.com/freeModels.jsp
https://www.sweethome3d.com/importTextures.jsp
and the translations proposed by contributors at:
http://www.sweethome3d.com/translations.jsp
Note that some recent pieces of furniture might not be translated in your language.

The Sweet Home 3D applications of this portable version are configured to store 
user preferences and imported data in the sub folder named "data", which lets you 
use Sweet Home 3D and its configuration from a computer to another, by just copying 
the current folder. 
Write rights must be granted to the "data" sub folder to let the application save 
preferences and imported data. You can copy the content of the "data" sub folder 
from a device to another if you need to copy a configuration of Sweet Home 3D. 
Note also that the "data" sub folder matches the default configuration folder used 
by the other versions of Sweet Home 3D (installer, JAR executable file and Java Web 
Start versions), i.e.:
- C:\Users\<USER>\AppData\Roaming\eTeks\Sweet Home 3D 
  folder under Windows Vista / 7 / 8 / 10 / 11,
- C:\Documents and Settings\user\Application Data\eTeks\Sweet Home 3D 
  folder under Windows XP and previous versions of Windows,
- Library/Application Support/eTeks/Sweet Home 3D
  sub folder of your user folder under Mac OS X, 
- .eteks/sweethome3d 
   sub folder of your user folder under Linux.   
If you need to restore the configuration of Sweet Home 3D you generated with another 
version of Sweet Home 3D, you can copy the content of the previous folders to 
the "data" sub folder of the portable version, except the sub folder named "work".
Whether you use this portable version or the other ones of Sweet Home 3D, a SH3D 
file is autonomous and contains all the required data to be edited whatever 
furniture or textures were imported on a computer.

Plugins SH3P files are supported by this version of Sweet Home 3D and should be 
placed in the "data/plugins" sub folder.

CAUTION: YOU SHOULD NOT MOVE FILES FOUND IN FOLDERS DIFFERENT FROM THE ONE 
         NAMED "data" OR SWEET HOME 3D MAY NOT WORK. NEVERTHELESS, IF YOU 
         WANT TO SAVE DISK SPACE, YOU MAY REMOVE THE SUB FOLDERS 
         OF THE "runtime" FOLDER WHICH DON'T MATCH 
         THE OPERATING SYSTEMS YOU NEED.          

Note that from version 7.3, macOS support is NOT provided anymore in this portable version
because recent macOS versions refuse to write in data folder for security reasons

Visit http://www.sweethome3d.com to get more information on Sweet Home 3D
   
Sweet Home 3D, Copyright (c) 2024 Space Mushrooms 
Distributed under GNU General Public License    