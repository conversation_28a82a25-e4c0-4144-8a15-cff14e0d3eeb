#!/bin/sh

# Retrieve Sweet Home 3D directory
PROGRAM=`readlink "$0"`
if [ "$PROGRAM" = "" ]; then
  PROGRAM=$0
fi
PROGRAM_DIR=`dirname "$PROGRAM"`

# Run Sweet Home 3D
exec "$PROGRAM_DIR"/runtime/linux/x64/runtime/bin/java -Xmx1024m -classpath "$PROGRAM_DIR"/lib/SweetHome3D.jar:"$PROGRAM_DIR"/lib/Furniture.jar:"$PROGRAM_DIR"/lib/Textures.jar:"$PROGRAM_DIR"/lib/Examples.jar:"$PROGRAM_DIR"/lib/Help.jar:"$PROGRAM_DIR"/lib/batik-svgpathparser-1.7.jar:"$PROGRAM_DIR"/lib/jeksparser-calculator.jar:"$PROGRAM_DIR"/lib/iText-2.1.7.jar:"$PROGRAM_DIR"/lib/freehep-vectorgraphics-svg-2.1.1c.jar:"$PROGRAM_DIR"/lib/sunflow-0.07.3i.jar:"$PROGRAM_DIR"/lib/jmf.jar:"$PROGRAM_DIR"/lib/java3d-1.6/j3dcore.jar:"$PROGRAM_DIR"/lib/java3d-1.6/j3dutils.jar:"$PROGRAM_DIR"/lib/java3d-1.6/vecmath.jar:"$PROGRAM_DIR"/lib/java3d-1.6/gluegen-rt.jar:"$PROGRAM_DIR"/lib/java3d-1.6/jogl-java3d.jar:"$PROGRAM_DIR"/lib/jnlp.jar -Djava.library.path="$PROGRAM_DIR"/lib/java3d-1.6/linux/amd64:"$PROGRAM_DIR"/lib/yafaray/linux/x64 -Djogamp.gluegen.UseTempJarCache=false -Dcom.eteks.sweethome3d.preferencesFolder="$PROGRAM_DIR"/data -Dcom.eteks.sweethome3d.applicationFolders="$PROGRAM_DIR"/data -Dcom.eteks.sweethome3d.deploymentInformation=portable -Dcom.eteks.sweethome3d.applicationId=SweetHome3D#Portable com.eteks.sweethome3d.SweetHome3D -open "$1"
